package be.fgov.onerva.cu.backend.integration

import java.sql.Date
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.jdbc.Sql
import be.fgov.onerva.cu.backend.CuBaseIntegration
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.port.`in`.ChangePersonalDataRequestUseCase
import be.fgov.onerva.cu.backend.application.port.out.FeatureFlagPort
import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.person.rest.model.CitizenDTO
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef
import be.fgov.onerva.unemployment.c9.rest.model.BelgianCommunity
import be.fgov.onerva.unemployment.c9.rest.model.C9
import be.fgov.onerva.unemployment.c9.rest.model.EC1
import be.fgov.onerva.unemployment.c9.rest.model.EC1Identity
import be.fgov.onerva.unemployment.c9.rest.model.EC1ModeOfPayment
import be.fgov.onerva.unemployment.c9.rest.model.EC1TradeUnionContribution
import be.fgov.onerva.unemployment.c9.rest.model.NationalityBCSS
import be.fgov.onerva.wo.facade.rest.model.StateDTO
import be.fgov.onerva.wo.facade.rest.model.StatusDTO
import be.fgov.onerva.wo.facade.rest.model.TaskDTO
import be.fgov.onerva.wo.organizational.chart.rest.model.Node
import io.mockk.every

@Sql(scripts = ["/cleanup.sql"])
class ChangePersonalDataRequestMessageIT : CuBaseIntegration() {
    @Autowired
    lateinit var changePersonalDataRequestUseCase: ChangePersonalDataRequestUseCase

    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    lateinit var jdbcHelper: JdbcHelper

    @Test
    fun `should return change of address message - C1`() {
        // given
        val ssin = "123456789"
        val c9id = 12345L
        val assignee = "cu_user"
        val changePersonalDataCommand = ChangePersonalDataRequestReceivedCommand(
            c9Id = c9id,
            type = "400",
            ssin = ssin,
            ec1Id = null,
        )
        val expectedNumbox = 42
        val citizenDTO = CitizenDTO(numbox = expectedNumbox)
        val taskDto = TaskDTO(
            concernedEntities = emptyList(),
            assignee = "test-assignee",
            taskTypeCode = "test-type",
            processId = 87876,
            taskId = 65544,
            assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
            status = StatusDTO(
                task = StateDTO.OPEN,
                process = StateDTO.OPEN
            )
        )
        val nodeResult = Node()

        every { client?.getBooleanValue(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
        every { client?.getBooleanValue(FeatureFlagPort.SUPPORTED_C9_TYPES, false) } returns true
        every { client?.getStringValue(FeatureFlagPort.SUPPORTED_C9_TYPES, "") } returns "400,410"

        every { c9Api?.getC9s(c9id.toInt()) } returns C9().apply {
            this.id = c9id
            this.ssin = ssin
            this.type = "400"
            this.requestDate = LocalDate.of(2021, 1, 1)
            this.introductionDate = LocalDate.of(2021, 1, 1)
            this.dateValid = LocalDate.of(2024, 12, 15)
            this.opKey = "OP123"
            this.sectOp = "SO123"
            this.paymentInstitution = 778899
            this.entityCode = "EC123"
            this.scanNumber = 778866L
            this.attestRefs = emptyList()
        }
        every { citizenApi?.getByNiss(ssin) } returns citizenDTO
        every { facadeControllerApi?.createTask(any()) } returns taskDto
        every { defaultApi?.createParty(any(), isNull()) } returns null
        every { nodeApi?.createNode(any()) } returns nodeResult
        every { currentUserPort?.getCurrentUsername() } returns assignee

        // when
        changePersonalDataRequestUseCase.receivedChangePersonalData(changePersonalDataCommand)

        // then
        val requestMap = jdbcHelper.getRequestByC9Id(c9id)

        assertThat(requestMap)
            .containsEntry("ssin", ssin)
            .containsEntry("c9_id", c9id)
            .containsEntry("sect_op", "SO123")
            .containsEntry("op_key", "OP123")
            .containsEntry("document_type", "PAPER")
            .containsEntry("type", "CHANGE_PERSONAL_DATA")
        val requestId = requestMap.get("id")
        assertThat(
            jdbcTemplate.queryForList(
                """
            select * from citizen_information where request_id = ?
        """.trimIndent(),
                requestId
            )
        ).isEmpty()
        assertThat(
            jdbcTemplate.queryForList(
                """
            select * from mode_of_payment where request_id = ?
        """.trimIndent(),
                requestId
            )
        ).isEmpty()
        assertThat(
            jdbcTemplate.queryForList(
                """
            select * from union_contribution where request_id = ?
        """.trimIndent(),
                requestId
            )
        ).isEmpty()
    }

    @Test
    fun `should return change of address message - EC1`() {
        // given
        val ssin = "123456789"
        val c9id = 12346L
        val assignee = "cu_user"
        val changePersonalDataCommand = ChangePersonalDataRequestReceivedCommand(
            c9Id = c9id,
            type = "400",
            ssin = ssin,
            ec1Id = 1234,
        )
        val ec1 = EC1().apply {
            identity = EC1Identity().apply {
                firstName = "John"
                lastName = "Doe"
                dateOfBirth = "1990-01-01"
                nationality = NationalityBCSS().apply {
                    code = "BE"
                }
                street = "the-street"
                houseNumber = "the-housenumber"
                boxNumber = "the-box-number"
                zipCode = BelgianCommunity().apply {
                    zipCode = "1000"
                }
                city = "Brussels"
                country = NationalityBCSS().apply {
                    code = "BE"
                }
            }
            modeOfPayment = EC1ModeOfPayment().apply {
                isMyBankAccount = true
                foreignBankAccountIBAN = null
                bankAccountForOtherPersonName = null
                belgianSEPABankAccount = "BE776655443232"
                foreignBankAccountBIC = null
            }
            tradeUnionContribution = EC1TradeUnionContribution().apply {
                stopContributionDeductionFromTheMonth = LocalDate.of(2025, 2, 1)
                contributionDeductionFromTheMonth = null
            }
        }
        val expectedNumbox = 42
        val citizenDTO = CitizenDTO(numbox = expectedNumbox)
        val taskDto = TaskDTO(
            concernedEntities = emptyList(),
            assignee = "test-assignee",
            taskTypeCode = "test-type",
            processId = 87876,
            taskId = 65544,
            assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
            status = StatusDTO(
                task = StateDTO.OPEN,
                process = StateDTO.OPEN
            )
        )
        val nodeResult = Node()

        every { client?.getBooleanValue(FeatureFlagPort.CHANGE_OF_ADDRESS_ENABLED, false) } returns true
        every { client?.getBooleanValue(FeatureFlagPort.SUPPORTED_C9_TYPES, false) } returns true
        every { client?.getStringValue(FeatureFlagPort.SUPPORTED_C9_TYPES, "") } returns "400,410"

        every { c9Api?.getC9s(c9id.toInt()) } returns C9().apply {
            this.id = c9id
            this.ssin = ssin
            this.type = "400"
            this.requestDate = LocalDate.of(2021, 1, 1)
            this.introductionDate = LocalDate.of(2021, 1, 1)
            this.dateValid = LocalDate.of(2024, 12, 15)
            this.opKey = "OP123"
            this.sectOp = "SO123"
            this.paymentInstitution = 778899
            this.entityCode = "EC123"
            this.scanNumber = 778866L
            this.attestRefs = listOf(AttestRef().apply {
                type = "EC1"
                id = 1234
                displayUrl = "http://example.com"
            })
        }
        every { ec1Api?.getC1s(1234) } returns ec1
        every { citizenApi?.getByNiss(ssin) } returns citizenDTO
        every { facadeControllerApi?.createTask(any()) } returns taskDto
        every { defaultApi?.createParty(any(), isNull()) } returns null
        every { nodeApi?.createNode(any()) } returns nodeResult
        every { currentUserPort?.getCurrentUsername() } returns assignee

        // when
        changePersonalDataRequestUseCase.receivedChangePersonalData(changePersonalDataCommand)

        // then
        val requestMap = jdbcTemplate.queryForMap(
            """
            SELECT * 
            FROM request 
            WHERE c9_id = ?
            """,
            c9id
        )
        assertThat(requestMap)
            .containsEntry("ssin", ssin)
            .containsEntry("c9_id", c9id)
            .containsEntry("sect_op", "SO123")
            .containsEntry("op_key", "OP123")
            .containsEntry("document_type", "ELECTRONIC")
            .containsEntry("type", "CHANGE_PERSONAL_DATA")

        val requestId = requestMap.get("id")

        val citizenInformationMap = jdbcTemplate.queryForMap(
            """
            select * from citizen_information where request_id = ?
        """.trimIndent(),
            requestId
        )
        assertThat(citizenInformationMap)
            .containsEntry("request_id", requestId)
            .containsEntry("birth_date", Date.valueOf("1990-01-01"))
            .containsEntry("nationality", "BE")
            .containsEntry("street", "the-street")
            .containsEntry("box_number", "the-box-number")
            .containsEntry("house_number", "the-housenumber")
            .containsEntry("zip_code", "1000")
            .containsEntry("city", "Brussels")
            .containsEntry("country", "BE")
            .containsEntry("update_status", "FROM_C9")

        val modeOfPaymentMap = jdbcTemplate.queryForMap(
            """
            select * from mode_of_payment where request_id = ?
        """.trimIndent(),
            requestId
        )
        assertThat(modeOfPaymentMap)
            .containsEntry("request_id", requestId)
            .containsEntry("other_person_name", null)
            .containsEntry("iban", "BE776655443232")
            .containsEntry("bic", null)
            .containsEntry("update_status", "FROM_C9")

        val unionContributionMap = jdbcTemplate.queryForMap(
            """
            select * from union_contribution where request_id = ?
        """.trimIndent(),
            requestId
        )
        println(unionContributionMap)
        assertThat(unionContributionMap)
            .containsEntry("request_id", requestId)
            .containsEntry("authorized", false)
            .containsEntry("effective_date", Date.valueOf("2025-02-01"))
            .containsEntry("update_status", "FROM_C9")
    }
}