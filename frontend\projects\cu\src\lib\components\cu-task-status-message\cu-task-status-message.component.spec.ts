import {ComponentFixture, TestBed} from "@angular/core/testing";
import {RequestBasicInfoResponse} from "@rest-client/cu-bff";
import {CuTaskStatusMessageComponent} from "./cu-task-status-message.component";
import {MatIconModule} from "@angular/material/icon";
import {TranslateModule} from "@ngx-translate/core";
import {OnemrvaThemeModule} from "@onemrvapublic/design-system-theme";
import {NO_ERRORS_SCHEMA} from "@angular/core";
import {By} from "@angular/platform-browser";
import {FormUtilsService} from "../../services/form-utils.service";

describe("CuTaskStatusMessageComponent", () => {
    let component: CuTaskStatusMessageComponent;
    let fixture: ComponentFixture<CuTaskStatusMessageComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                CuTaskStatusMessageComponent,
                MatIconModule,
                TranslateModule.forRoot(),
                OnemrvaThemeModule,
            ],
            schemas: [NO_ERRORS_SCHEMA],
        }).compileComponents();

        fixture = TestBed.createComponent(CuTaskStatusMessageComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("should create the component", () => {
        expect(component).toBeTruthy();
    });

    describe("isTreatedOnMainFrame", () => {
        it("should return true when status is CLOSED and decisionType exists", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";

            expect(component.isTreatedOnMainFrame()).toBe(true);
        });

        it("should return false when status is not CLOSED", () => {
            component.status = "OPEN";
            component.decisionType = "TYPE1";

            expect(component.isTreatedOnMainFrame()).toBe(false);
        });

        it("should return false when decisionType is undefined", () => {
            component.status = "CLOSED";
            component.decisionType = undefined;

            expect(component.isTreatedOnMainFrame()).toBe(false);
        });

        it("should return false when decisionType is empty string", () => {
            component.status = "CLOSED";
            component.decisionType = "";

            expect(component.isTreatedOnMainFrame()).toBe(false);
        });
    });

    describe("isClosedAndNotTreated", () => {
        beforeEach(() => {
            jest.spyOn(FormUtilsService, "isClosedOrWaiting").mockImplementation((status, task) => {
                return status === "CLOSED" || status === "WAITING";
            });
        });

        it("should return true when status is CLOSED, not treated on main frame, and next task info exists", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";

            expect(component.isClosedAndNotTreated()).toBe(true);
        });

        it("should return false when status is not CLOSED or WAITING", () => {
            component.status = "OPEN";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when treated on main frame", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "Action";

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when nextTaskDescription is empty", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "";
            component.nextTaskAction = "Action";

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when nextTaskAction is empty", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next task";
            component.nextTaskAction = "";

            expect(component.isClosedAndNotTreated()).toBe(false);
        });

        it("should return false when both next task fields are empty", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "";
            component.nextTaskAction = "";

            expect(component.isClosedAndNotTreated()).toBe(false);
        });
    });

    describe("isLogiclyDeleted", () => {
        it("should call FormUtilsService.isLogiclyDeleted with task parameter", () => {
            const mockTask = "TASK_123";
            component.task = mockTask;

            const spy = jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(true);

            const result = component.isLogiclyDeleted();

            expect(spy).toHaveBeenCalledWith(mockTask);
            expect(result).toBe(true);
        });
    });

    describe("Input properties", () => {
        it("should properly set all input properties", () => {
            const testData = {
                status: "CLOSED",
                task: "TASK_123",
                decisionType: "TYPE1",
                decisionBarema: "BAREMA1",
                nextTaskDescription: "Description",
                nextTaskAction: "Action",
            };

            component.status = testData.status;
            component.task = testData.task;
            component.decisionType = testData.decisionType;
            component.decisionBarema = testData.decisionBarema;
            component.nextTaskDescription = testData.nextTaskDescription;
            component.nextTaskAction = testData.nextTaskAction;
            fixture.detectChanges();

            expect(component.status).toBe(testData.status);
            expect(component.task).toBe(testData.task);
            expect(component.decisionType).toBe(testData.decisionType);
            expect(component.decisionBarema).toBe(testData.decisionBarema);
            expect(component.nextTaskDescription).toBe(testData.nextTaskDescription);
            expect(component.nextTaskAction).toBe(testData.nextTaskAction);
        });

        it("should properly set pushbackStatus input", () => {
            const testStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            component.pushbackStatus = testStatus;
            fixture.detectChanges();

            expect(component.pushbackStatus).toBe(testStatus);
        });
    });

    describe("isNotClosed", () => {
        it("should return true when status is not CLOSED", () => {
            component.status = "OPEN";
            expect(component.isNotClosed()).toBe(true);
        });

        it("should return true when status is PENDING", () => {
            component.status = "PENDING";
            expect(component.isNotClosed()).toBe(true);
        });

        it("should return false when status is CLOSED", () => {
            component.status = "CLOSED";
            expect(component.isNotClosed()).toBe(false);
        });

        it("should return true when status is undefined", () => {
            component.status = undefined;
            expect(component.isNotClosed()).toBe(true);
        });
    });

    describe("showMainFrameMessage", () => {
        it("should return true when status is not CLOSED", () => {
            component.status = "OPEN";
            expect(component.showMainFrameMessage()).toBe(true);
        });

        it("should return false when status is CLOSED", () => {
            component.status = "CLOSED";
            expect(component.showMainFrameMessage()).toBe(false);
        });
    });

    describe("Mainframe response rendering with message boxes", () => {
        it("should show success message box when pushbackStatus is OK and not closed", () => {
            component.status = "OPEN";
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el => el.nativeElement.getAttribute("color") === "success");

            expect(successBox).toBeTruthy();
            expect(successBox?.nativeElement.textContent).toContain("MAINFRAME_RESPONSE.OK");
        });

        it("should show error message box when pushbackStatus is NOK and not closed", () => {
            component.status = "OPEN";
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Nok;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const errorBox = messageBoxes.find(el => el.nativeElement.getAttribute("color") === "error");

            expect(errorBox).toBeTruthy();
            expect(errorBox?.nativeElement.textContent).toContain("MAINFRAME_RESPONSE.NOK");
        });

        it("should show warning message box when pushbackStatus is PENDING and not closed", () => {
            component.status = "OPEN";
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Pending;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const warnBox = messageBoxes.find(el => el.nativeElement.getAttribute("color") === "warn");

            expect(warnBox).toBeTruthy();
            expect(warnBox?.nativeElement.textContent).toContain("MAINFRAME_RESPONSE.PENDING");
        });

        it("should not show mainframe messages when status is CLOSED", () => {
            component.status = "CLOSED";
            component.pushbackStatus = RequestBasicInfoResponse.PushbackStatusEnum.Ok;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            // Should only show message boxes for other conditions, not mainframe status
            messageBoxes.forEach(box => {
                expect(box.nativeElement.textContent).not.toContain("MAINFRAME_RESPONSE");
            });
        });

        it("should handle undefined pushbackStatus gracefully", () => {
            component.status = "OPEN";
            component.pushbackStatus = undefined;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            messageBoxes.forEach(box => {
                expect(box.nativeElement.textContent).not.toContain("MAINFRAME_RESPONSE");
            });
        });
    });

    describe("Treated on mainframe message rendering", () => {
        it("should show success message box when treated on mainframe", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.decisionBarema = "BAREMA1";
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("TREATED_ON_MAINFRAME.CLOSED"),
            );

            expect(successBox).toBeTruthy();
            expect(successBox?.nativeElement.textContent).toContain("TREATED_ON_MAINFRAME.DECISION");
            expect(successBox?.nativeElement.textContent)
                .toContain("TREATED_ON_MAINFRAME.CODE." + component.decisionType);
            expect(successBox?.nativeElement.textContent).toContain("TREATED_ON_MAINFRAME.BAREMA");
            expect(successBox?.nativeElement.textContent).toContain("BAREMA1");
        });

        it("should display N/A when decisionBarema is not provided", () => {
            component.status = "CLOSED";
            component.decisionType = "TYPE1";
            component.decisionBarema = undefined;
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("TREATED_ON_MAINFRAME"),
            );

            expect(successBox).toBeTruthy();
            expect(successBox?.nativeElement.textContent).toContain("N/A");
        });
    });

    describe("Closed and not treated message rendering", () => {
        it("should show success message box when closed and not treated", () => {
            component.status = "CLOSED";
            component.decisionType = "";
            component.nextTaskDescription = "Next Task Description";
            component.nextTaskAction = "Next Task Action";

            jest.spyOn(FormUtilsService, "isClosedOrWaiting").mockReturnValue(true);

            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const successBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "success" &&
                el.nativeElement.textContent.includes("Next Task Description"),
            );

            expect(successBox).toBeTruthy();
            expect(successBox?.nativeElement.textContent).toContain("Next Task Action");
        });
    });

    describe("Logically deleted message rendering", () => {
        it("should show warning message box when logically deleted", () => {
            component.task = "DELETED_TASK";
            jest.spyOn(FormUtilsService, "isLogiclyDeleted").mockReturnValue(true);
            fixture.detectChanges();

            const messageBoxes = fixture.debugElement.queryAll(By.css("onemrva-mat-message-box"));
            const warnBox = messageBoxes.find(el =>
                el.nativeElement.getAttribute("color") === "warn" &&
                el.nativeElement.querySelector(".error-title"),
            );

            expect(warnBox).toBeTruthy();
            const errorTitle = warnBox?.nativeElement.querySelector(".error-title");
            expect(errorTitle).toBeTruthy();
            expect(errorTitle.getAttribute("style")).toContain("margin: 0 0 0.5rem 0");
        });
    });

    describe("processLink and ngOnChanges", () => {
        beforeEach(() => {
            jest.spyOn(FormUtilsService, "getWaveProcessUrl").mockImplementation((task) => {
                if (!task?.parentProcess?.processId) {
                    return "";
                }
                return `/processes-page/process/(process-detail/${task.parentProcess.processId}!!sidemenu:process-detail/${task.parentProcess.processId})`;
            });
        });

        it("should initialize processLink as empty string", () => {
            expect(component.processLink).toBe("");
        });

        it("should update processLink when task input changes", () => {
            const mockTask = {
                parentProcess: {
                    processId: "12345",
                },
            };

            component.task = mockTask;

            component.ngOnChanges({
                task: {
                    currentValue: mockTask,
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).toHaveBeenCalledWith(mockTask);
            expect(component.processLink)
                .toBe("/processes-page/process/(process-detail/12345!!sidemenu:process-detail/12345)");
        });

        it("should not update processLink when task is null", () => {
            component.processLink = "existing-link";
            component.task = null;  // Set task to null

            component.ngOnChanges({
                task: {
                    currentValue: null,
                    previousValue: {id: "old"},
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
            expect(component.processLink).toBe("existing-link");
        });

        it("should not update processLink when task is undefined", () => {
            component.processLink = "existing-link";
            component.task = undefined;

            component.ngOnChanges({
                task: {
                    currentValue: undefined,
                    previousValue: {id: "old"},
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
            expect(component.processLink).toBe("existing-link");
        });

        it("should handle task without processId", () => {
            const mockTask = {
                id: "task123",
            };

            component.task = mockTask;
            component.ngOnChanges({
                task: {
                    currentValue: mockTask,
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).toHaveBeenCalledWith(mockTask);
            expect(component.processLink).toBe("");
        });

        it("should not update processLink when other inputs change", () => {
            component.processLink = "existing-link";
            const getWaveProcessUrlSpy = jest.spyOn(FormUtilsService, "getWaveProcessUrl");

            component.ngOnChanges({
                status: {
                    currentValue: "CLOSED",
                    previousValue: "OPEN",
                    firstChange: false,
                    isFirstChange: () => false,
                },
                decisionType: {
                    currentValue: "TYPE1",
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(getWaveProcessUrlSpy).not.toHaveBeenCalled();
            expect(component.processLink).toBe("existing-link");
        });

        it("should update processLink when task changes multiple times", () => {
            const firstTask = {
                parentProcess: {processId: "111"},
            };
            const secondTask = {
                parentProcess: {processId: "222"},
            };

            component.task = firstTask;
            component.ngOnChanges({
                task: {
                    currentValue: firstTask,
                    previousValue: null,
                    firstChange: true,
                    isFirstChange: () => true,
                },
            });

            expect(component.processLink)
                .toBe("/processes-page/process/(process-detail/111!!sidemenu:process-detail/111)");

            component.task = secondTask;
            component.ngOnChanges({
                task: {
                    currentValue: secondTask,
                    previousValue: firstTask,
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(component.processLink)
                .toBe("/processes-page/process/(process-detail/222!!sidemenu:process-detail/222)");
            expect(FormUtilsService.getWaveProcessUrl).toHaveBeenCalledTimes(2);
        });

        it("should only check task property in changes object", () => {
            const mockTask = {
                parentProcess: {processId: "999"},
            };
            component.task = mockTask;

            component.ngOnChanges({
                status: {
                    currentValue: "CLOSED",
                    previousValue: "OPEN",
                    firstChange: false,
                    isFirstChange: () => false,
                },
            });

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
        });

        it("should not update processLink when changes does not contain task", () => {
            const mockTask = {
                parentProcess: {processId: "999"},
            };
            component.task = mockTask;
            component.processLink = "existing-link";

            component.ngOnChanges({});

            expect(FormUtilsService.getWaveProcessUrl).not.toHaveBeenCalled();
            expect(component.processLink).toBe('existing-link');
        });
    });
});