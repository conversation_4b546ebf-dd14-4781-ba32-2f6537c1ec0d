trigger:
  branches:
    include:
      - '*'

pool: 'Azure-UbuntuMinimal'

resources:
  repositories:
    - repository: templates
      type: git
      name: self-services-build-templates/self-services-build-templates
      ref: refs/heads/develop

variables:
  - group: ProjectVariables
  - group: ManagedVault
  - group: ManagedVariables
  - name: isDevelop
    value: $[eq(variables['Build.SourceBranch'], 'refs/heads/develop')]
  - name: isAllowedBranch
    value: $[or(eq(variables['Build.SourceBranch'], 'refs/heads/develop'), eq(variables['Build.SourceBranch'], 'refs/heads/feature/XXX'))]

stages:
  - stage: BuildOnAzure
    displayName: Build on Azure
    jobs:
      - template: buildsh/build/stages/jobs/build.yaml@templates
        parameters:
          jobName: Build
          scripts:
            - './build.sh -p ci next-version'
            - './build.sh -p ci test'
            - './build.sh -p ci build'
            - './build.sh -p ci publish'
          additionalSteps:
            - publish: helm/environments
              artifact: environments
            - publish: target/metadata.yaml
              artifact: metadata
            # Common coverage
            - publish: common/target/jacoco.exec
              artifact: jacoco_unit_common
            # Backend coverage
            - publish: backend/target/jacoco.exec
              artifact: jacoco_unit_backend
            # BFF coverage
            - publish: bff/target/jacoco.exec
              artifact: jacoco_unit_bff
          publishDefaultArtifacts: false

  - stage: E2ETestsOnAzure
    dependsOn: BuildOnAzure
    displayName: Run E2E Tests on Azure
    variables:
      - group: "ci"
    lockBehavior: sequential
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          environment: ci
          helmValuesPath: target/helm/environments
          displayName: "Deploy & Run CI Tests"
          additionalSteps:
            - template: buildsh/run-script.yaml@templates
              parameters:
                script: ./build.sh -p ci e2e-tests
                displayName: "Run CI Tests"
            - template: buildsh/run-script.yaml@templates
              parameters:
                script: ./build.sh -p ci undeploy
                displayName: "Undeploy CI"
            # Backend E2E coverage
            - publish: backend/target/jacoco-it.exec
              artifact: jacoco_it_backend
            # BFF E2E coverage - new
            - publish: bff/target/jacoco-it.exec
              artifact: jacoco_it_bff

  - stage: QualityAnalysis
    dependsOn: E2ETestsOnAzure
    displayName: Quality Analysis on Azure
    condition: and(succeeded('E2ETestsOnAzure'), eq(variables.isDevelop, true))
    jobs:
      - template: buildsh/scripts/stages/jobs/scripts.yaml@templates
        parameters:
          jobName: SonarQube
          displayName: Run Code Quality Analysis
          preSteps:
            # Download backend coverage files
            - task: DownloadPipelineArtifact@2
              inputs:
                buildType: 'current'
                artifact: jacoco_unit_common
                path: '$(Build.SourcesDirectory)/common/target'
            - task: DownloadPipelineArtifact@2
              inputs:
                buildType: 'current'
                artifact: jacoco_unit_backend
                path: '$(Build.SourcesDirectory)/backend/target'
            - task: DownloadPipelineArtifact@2
              inputs:
                buildType: 'current'
                artifact: jacoco_it_backend
                path: '$(Build.SourcesDirectory)/backend/target'
            # Download BFF coverage files - new
            - task: DownloadPipelineArtifact@2
              inputs:
                buildType: 'current'
                artifact: jacoco_unit_bff
                path: '$(Build.SourcesDirectory)/bff/target'
            - task: DownloadPipelineArtifact@2
              inputs:
                buildType: 'current'
                artifact: jacoco_it_bff
                path: '$(Build.SourcesDirectory)/bff/target'
          scripts:
            - './build.sh -p ci sonarqube'

  - stage: DeployOnTest
    dependsOn: BuildOnAzure
    displayName: Deploy on test
    condition: and(succeeded('BuildOnAzure'), eq(variables.isAllowedBranch, true))
    variables:
      - group: "test"
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          extraEnv:
            security_oauth2_client_registration_keycloak_clientsecret: $(security_oauth2_client_registration_keycloak_clientsecret)
            spring_datasource_password: $(spring_datasource_password)
            keycloak_admin_password: $(keycloak_admin_password)
            spring_backend_keycloak_clientsecret: $(spring_backend_keycloak_clientsecret)
            spring_bff_keycloak_clientsecret: $(spring_bff_keycloak_clientsecret)
          environment: test
          helmValuesPath: "target/helm/environments"

  - stage: DeployOnVal
    dependsOn:
      - DeployOnTest
      - QualityAnalysis
    condition: and(succeeded('DeployOnTest'), succeeded('QualityAnalysis'), eq(variables.isDevelop, true))
    displayName: Deploy on val
    variables:
      - group: "val"
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          extraEnv:
            security_oauth2_client_registration_keycloak_clientsecret: $(security_oauth2_client_registration_keycloak_clientsecret)
            spring_datasource_password: $(spring_datasource_password)
            keycloak_admin_password: $(keycloak_admin_password)
            spring_backend_keycloak_clientsecret: $(spring_backend_keycloak_clientsecret)
            spring_bff_keycloak_clientsecret: $(spring_bff_keycloak_clientsecret)
          environment: val
          helmValuesPath: "target/helm/environments"

  - stage: DeployOnProd
    dependsOn: DeployOnVal
    condition: and(succeeded('DeployOnVal'), eq(variables.isDevelop, true))
    displayName: Deploy on prod
    variables:
      - group: "prod"
    jobs:
      - template: buildsh/deploy/stages/jobs/deploy.yaml@templates
        parameters:
          extraEnv:
            security_oauth2_client_registration_keycloak_clientsecret: $(security_oauth2_client_registration_keycloak_clientsecret)
            spring_datasource_password: $(spring_datasource_password)
            keycloak_admin_password: $(keycloak_admin_password)
            spring_backend_keycloak_clientsecret: $(spring_backend_keycloak_clientsecret)
            spring_bff_keycloak_clientsecret: $(spring_bff_keycloak_clientsecret)
          environment: prod
          helmValuesPath: "target/helm/environments"