{"CU_CALCULATION": {"MAIN_TITLE": "In het Nederlands", "CU_DETAILS": {"EMPLOYEE": {"EMPLOYEE": "Werknemer", "INSS": "Rijksregisternummer", "BIRTHDATE": "Geboortedatum", "LASTNAME": "<PERSON><PERSON>", "FIRSTNAME": "<PERSON><PERSON><PERSON><PERSON>", "NATIONALITY": "Nationaliteit", "ADDRESS": "<PERSON><PERSON>", "COUNTRY": "Land"}, "EMPLOYER": {"EMPLOYER": "Werkgever", "BCE": "KBO nummer", "JOINT_COMMITTEE": "Paritair comité", "NACE": "NACE code", "NOSS": "Inschrijvingsnr RSZ", "NAME": "<PERSON><PERSON>"}, "CONTRACT": {"CONTRACT": "Arbeidsovereenkomst", "START_DATE": "Begindatum arbeidsovereenkomst", "Q_S": "Q/S (arbeidsduur)", "SALARY": "Loon", "SALARYTYPE": {"HOUR": "per uur", "DAY": "per dag", "WEEK": "per week", "MONTH": "per maand", "TRIMESTER": "per trimester", "YEAR": "per jaar", "UNKNOWN": "niet geweten"}, "PERSALARYTYPE": {"1": "uur", "2": "dagen", "3": "weken", "4": "ma<PERSON>en", "5": "kwart", "6": "jaar", "UNKNOWN": "niet geweten"}, "START_DATE_TU": "Begindatum Tijdelijke Werkloosheid", "TYPE": "Type TW"}}, "CALCULATIONS": {"TITLE": "Berekening"}, "PREFIX_QUESTION_TOOLTIPS": {"CAREER_BREAK": "U kunt uw carrière onderbreken op de vraagdatum", "Q_S": "J<PERSON>, indien Q/S >= 1", "EARLY_RETIREMENT": "Ja, als prefix van het laatste barema = 18", "MAINTENANCE_OF_UNEMPLOYMENT": "", "REFERENCE_SALARY": "<PERSON><PERSON>, als ma<PERSON>loon >= referteloon"}, "QUESTION": {"PREFIX_TITLE": "Prefix", "ARG": "<PERSON><PERSON><PERSON>", "REQUEST_CT": "Aanvraag TW", "CAREER_BREAK": {"TITLE": "Loopbaanonderbreking?", "ALLOWANCE_DATE": "<PERSON><PERSON>", "BAREMA_DATE": "Uitkeringsaanvraag TW", "REQUEST_DATE": "<PERSON><PERSON>"}, "Q_S": {"TITLE": "Voltijdse tewerkstelling?", "Q_S_OCCUPATION": "Q/S (arbeidsduur)"}, "EARLY_RETIREMENT": {"TITLE": "Deeltijdse SWT (stelsel werkloosheid met bedrijfstoeslag)", "LAST_BAREMA": "<PERSON><PERSON><PERSON> barema"}, "MAINTENANCE_OF_UNEMPLOYMENT_WITH_WORK_HISTORY": {"TITLE": "Deeltij<PERSON><PERSON> arb<PERSON>d met <PERSON><PERSON><PERSON>", "ARG": "Inkomensgarantie-uitkering", "LAST_BAREMA": "<PERSON><PERSON><PERSON> besliste barema", "ARG_PREVIOUS": "Eerdere rechten behouden?", "LAST_BAREMA_ARG": "Laatste barema DBR", "SAME_WORK": "Identieke arbeidsovereenkomst voor dit barema", "CONTRACT_IDENTIC": "Contract gekoppeld aan de a<PERSON>v<PERSON>ag", "START_DATE": "Begindatum", "Q_S": "Q/S", "BCE": "KBO nummer", "EMPLOYEE_NAME": "<PERSON><PERSON> we<PERSON>", "DECISION_TYPE": "<PERSON>rt besluit"}, "MAINTENANCE_OF_UNEMPLOYMENT": {"TITLE": "Deeltij<PERSON><PERSON> arb<PERSON>d met <PERSON><PERSON><PERSON>", "ARG": "Inkomensgarantie-uitkering", "LAST_BAREMA": "<PERSON><PERSON><PERSON> besliste barema", "ARG_PREVIOUS": "Eerdere rechten behouden?", "LAST_BAREMA_ARG": "Laatste barema DBR", "SAME_WORK": "Identieke arbeidsovereenkomst voor dit barema", "CONTRACT_IDENTIC": "Contract gekoppeld aan de a<PERSON>v<PERSON>ag", "START_DATE": "Begindatum", "Q_S": "Q/S", "BCE": "KBO nummer", "EMPLOYEE_NAME": "<PERSON><PERSON> we<PERSON>", "DECISION_TYPE": "<PERSON>rt besluit"}, "REFERENCE_SALARY": {"TITLE": "Maandloon >= referteloon?", "MONTHLY_SALARY": "Maandloon", "REFERENCE_SALARY": "Referteloon werkloosheid"}, "YES": "<PERSON>a", "NO": "<PERSON><PERSON>", "MAYBE": "?"}, "CIPHER_CODE": {"TITLE": "Cijfercode", "SJM_FORMULA": {"TITLE": "Te gebruiken formule", "PREFIX_06_02": "gemiddeld dagloon = uurloon * Q / 6", "MONTHLY_SALARY_REFERENCE_SALARY": "gemiddeld dagloon = referteloon / 26", "PREFIX_04_18": "gemiddeld dagloon = uurloon * S / 6", "DETAILS": {"PREFIX": "Prefix", "MONTHLY_SALARY": "Maandloon", "REFERENCE_SALARY": "Referteloon werkloosheid"}}, "AVERAGE_WAGE": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> dag<PERSON>on", "DETAILS": {"SALARY": "Loon", "REFERENCE_SALARY": "referte", "MONTHLY": "Per maand", "HOURLY": "Per uur", "Q_S": "Q/S (arbeidsduur)"}}, "CIPHER_CODE": {"TITLE": "Cijfercode op basis van het loon", "DETAILS": {"SALARY_BOUNDS": "<PERSON><PERSON><PERSON> van het gemiddelde dagloon", "DEMAND_DATE": "<PERSON><PERSON>"}}, "MAINTENANCE_OF_CODE": {"TITLE": "<PERSON><PERSON><PERSON>", "DETAILS": {"PREFIX": "Prefix", "AGE_OVER_45": "Leeftijd >= 45 jaar", "START_DATE": "Begindatum arbeidsovereenkomst", "LAST_BAREMA_FU": "Laatste barema volledige werkloosheid", "PREVIOUS_CODE_CT": "Vorige cijfercode volledige werkloosheid"}}, "FINAL_CODE": {"TITLE": "Uiteindelijke cijfercode", "DETAILS": {"BASE_SALARY_CODE": "Cijfercode op basis van loon", "BASE_MAINTENANCE_CODE": "Cijfercode op basis van \"Behoud van cijfercode\""}}, "TOOLTIPS": {"FINAL_CODE": "We kunnen nu de finale cijfercode bepalen door te kijken welk cijfercode uit de voorgaande stappen de hoogste was.", "MAINTENANCE_OF_CODE": "In sommige gevallen heeft de burger het recht om een eerdere cijfercode te behouden. Dit bepalen we aan de hand van de prefix/vorige barema/leeftijd...", "CIPHER_CODE": "Op basis van het Gemiddeld Dagloon (GD) kunnen we nu de theoretische cijfercode afleiden. Later zullen we zien of het de definitieve wordt.", "AVERAGE_WAGE": "In de vorige stap hebben we de formule bepaald. In deze stap berekenen we het Gemiddeld Dagloon (GD) op basis van deze formule.", "ADW_FORMULA": "In eerste instantie willen we het Gemiddelde Dagloon (GD) bepalen. GD is een manier om het salaris te standaardiseren. Deze stap bepaalt de te gebruiken formule, op basis van de context."}}, "CU_BAREMA": {"DECISION": "Beslissing", "BAREMA": "<PERSON><PERSON>", "ARTICLE": "Artikel", "ASK_ALLOWANCE_DATE": "<PERSON><PERSON>", "ARTICLE_INDEMNISION": "Compensatiepost(en)", "VALIDATION_DATE": "Beslissingsdatum", "PREFIX": "Prefix", "ENCRYPTED_CODE": "Cijfercode", "DIGIT_ERROR": "Slechts 1 of 2 cijfers toegestaan.", "COMMENT": "Opmerking (optioneel)", "SAVE_DRAFT": "Kladversie opslaan", "SEND_C2": "Beslissing C2 versturen", "MODIFIER": "<PERSON><PERSON><PERSON><PERSON>"}, "CU_HISTORY": {"TITLE": "Historiek van 3 jaar", "BUTTON": {"OPEN_ALL": "Alles openen", "CLOSE_ALL": "<PERSON>es sluiten"}, "CAREER": {"TITLE": "Loopbaanonderbreking", "NO_HISTORY": "<PERSON><PERSON>"}, "WORK": {"TITLE": "Werk", "NO_HISTORY": "<PERSON><PERSON> histor<PERSON> van werk"}, "BAREMA": {"TITLE": "Histor<PERSON> van het laatste barema", "NO_HISTORY": "<PERSON><PERSON> historiek van het laatste barema", "LAST_BAREMA": "<PERSON><PERSON><PERSON> barema", "TYPE": {"FULL": "Laatste barema volledige werkloosheid", "TEMPORARY": "Laatste barema tijdelijke werkloosheid", "OTHER": "<PERSON><PERSON><PERSON> besliste barema"}}}, "MESSAGE": {"SUCCESS": "Update uitgevoerd", "ERROR": "Er is een fout opgetreden", "SEND_SUCCESS": "Succesvol verzonden"}, "BAREMA_CALCULATION": {"MESSAGE": {"ERROR": "Er is een fout opgetreden bij het berekenen van het barema."}}}, "CU_DATA_CAPTURE": {"BUTTONS": {"VALIDATE": "<PERSON><PERSON>er gegevens", "COMPLETE": "Compleet", "INCOMPLETE": "Incompleet"}, "NEXT_TASK": {"DESCRIPTION": "De gegeven<PERSON>dering is geval<PERSON><PERSON>. Ga nu verder met de volgende taak", "ACTION": "“Data consistentie”"}, "CDF": {"TITLE": "<PERSON><PERSON><PERSON>", "ADDRESS": "<PERSON><PERSON>", "REQUEST": {"TITLE": "Werkloosheidsaanvraag", "STARTDATE": "<PERSON><PERSON> van de <PERSON>lo<PERSON>"}, "EMPLOYEE": {"TITLE": "Identiteit van de werknemer", "NISS": "Rijksregister nummer", "BIRTHDAY": "Geboortedatum", "LASTNAME": "Achternaam", "FIRSTNAME": "<PERSON><PERSON><PERSON><PERSON>", "NATIONALITY": "Nationaliteit", "COUNTRY": "Land", "STREET": "Straat", "STREET_NUMBER": "<PERSON><PERSON><PERSON>", "STREET_BOX": "Bus", "POST_CODE": "Postcode", "CITY": "Gemeente"}, "NO_COUNTRY_FOUND": "Er zijn geen resultaten gevonden voor '{{country}}'", "NATIONALITY_FIELD_NOT_SELECTED": "U moet het \"Nationaliteitsveld\" typen en het land selecteren in de keuzelijst", "COUNTRY_FIELD_NOT_SELECTED": "U moet het \"Landveld\" typen en het land selecteren in de keuzelijst", "BANK": {"TITLE": "Bankrekening", "IN_NAME_OFF": "Deze rekening is", "IN_EMPLOYEE_NAME": "op naam van de werknemer", "IN_OTHER_NAME": "op naam van", "SEPA_ACCOUNT": "SEPA-rekening", "BELGIAN_ACCOUNT": "bel<PERSON><PERSON>", "FOREIGN_IBAN": "buitenlandse IBAN", "FOREIGN_BIC": "BIC"}, "SYNDICATE": {"TITLE": "Vakbondsbijdrage", "CONTRIBUTION": "De werkloze geeft toestemming voor het inhouden van de vakbondsbijdrage op de uitkeringen vanaf (eerste van de maand indien bestaand dossier; datum UA indien allereerste aanvraag) ", "STOP_CONTRIBUTION": "De werklo<PERSON> wil geen inhouding meer van de vakbondsbijdrage op de uitkeringen vanaf (eerste van de maand) ", "CONTRIBUTION_NOT_AUTHORIZED": "<PERSON><PERSON> verm<PERSON>"}, "TOOLTIPS": {"NAME": "In geval van verschillen tussen de achternaam en voornaam op formulier C1, gelieve contact op te nemen met de helpdesk"}}}, "CU_DATA_CONSISTENCY": {"BUTTONS": {"VALIDATE": "<PERSON><PERSON><PERSON><PERSON> valid<PERSON>", "SEND_C51": "Verstuur een c51", "OPEN_S24": "Mainframe openen", "SAVE_AS_DRAFT": "Concept opslaan", "COMPLETE": "<PERSON><PERSON><PERSON><PERSON>erd door het systeem", "VALIDATED_BY_AGENT": "<PERSON><PERSON><PERSON>ieerd door agent", "INCOMPLETE": "Te beoordelen", "CORRECT": "<PERSON><PERSON><PERSON><PERSON>", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>"}, "DC": {"TITLE": "Taak 2: Gegevensconsistentie", "SUB_TITLE": "Gegevens om te controleren", "TABLE": {"CITIZEN": {"TITLE": "<PERSON> de werknemer", "ROW": {"NISS": "NISS", "FULL_NAME": "Naam, Voornaam", "ADDRESS": "<PERSON><PERSON>", "BIRTHDATE": "Geboortedatum", "NATIONALITY": "Nationaliteit", "BANKACCOUNT": "Bankrekeningnummer", "TITULAIRE": "<PERSON><PERSON><PERSON>"}}, "UNION": {"TITLE": "Over de vakbond", "ROW": {"CONTRIBUTION": "Vakbondsbijdrage", "CONTRIBUTION_AUTHORIZATION": "Inhouding toegestaan", "CONTRIBUTION_NON_AUTHORIZATION": "Inhouding niet <PERSON>", "CONTRIBUTION_DATE": "<PERSON> toepassing vanaf"}}, "COLUMN": {"CITIZEN": "Gegevens (e)C1", "ONEM": "Gegevens Mainframe", "SOURCE_AUTHENTIQUES": "Gegevens KSZ", "ACTION": "Gekozen waarde"}}, "DIALOG": {"ORIGIN": {"EMPLOYEE": "Gecodeerd door de werknemer - C1", "ONEM": "ONEM database - Mainframe", "SOURCE_AUTHENTIQUES": "Authentieke bronnen - Regis", "EMPLOYER": "Gecodeerd door werkgever - WECH"}, "BODY": "Gegevens uit verschillende bronnen zijn niet <PERSON> met <PERSON><PERSON><PERSON>.", "VALUEDATE": "datum", "SUBTITLE": "Selecteer de juiste gegevens:", "ACTION": {"VALIDATE": "Valideren", "CANCEL": "<PERSON><PERSON><PERSON>"}}, "FAMILY": {"FAMILY_SITUATION": "Gezinssituatie", "LAST_DECISION": "Laatste beslissing", "REGIS": {"TITLE": "Regis scherm", "BUTTON": "Toegang tot het Regis-scherm", "READAKNOLEDGMENT": "Ik verk<PERSON>ar dat ik de gezinssamenstelling heb geverifieerd en de conformiteit C1 - Bijlage Regis heb geselecteerd"}}}, "NEXT_TASK": {"DESCRIPTION": "De gegevens zijn g<PERSON>. Ga nu verder met de volgende taak", "ACTION": "<PERSON><PERSON><PERSON><PERSON>”"}, "S24_DIALOG": {"TITLE": "Let op: Weet u zeker dat u wilt doorgaan?", "BODY_1": "U wordt doorgestuurd naar de", "BODY_2": "S24", "BODY_3": ", maar de eerder ingevoerde g<PERSON> in Wave ", "BODY_4": "worden niet naar de Mainframe verstuurd.", "PRIMARY": "Ja, ga naar S24", "SECONDARY": "<PERSON><PERSON><PERSON>", "TOOLTIP": "Let op: u moet een geopende T27-sessie hebben om deze link te kunnen activeren."}, "VALIDATION_DIALOG": {"TITLE": "Opgelet: Bent u zeker dat u wilt doorgaan?", "SUBTITLE": "<PERSON> moet een open T27-se<PERSON> hebben om toegang te krijgen tot het dossier.", "BODY_1": "1. Door door te gaan, verbindt u zich ertoe ", "BODY_2": "deze gegevens naar de Mainframe te verzenden.", "BODY_3": "2. U wordt doorverwezen naar de ", "BODY_4": "S24 om de verwerking van het dossier af te ronden", "BODY_5": "in het geval van een eventuele wij<PERSON>ing van de gezinssituatie, ", "BODY_6": "en de beslissing te valideren.", "FOOTER": "Deze actie is tij<PERSON>ijk tot de volgende update van de applicatie.", "PRIMARY": "<PERSON><PERSON>, valideren en doorgaan in de S24", "SECONDARY": "<PERSON><PERSON><PERSON>"}}, "CU_ROUTING": {"BUTTONS": {"VALIDATE": "Ga verder in de Wave", "MAINFRAME": "Ga verder in de mainframe"}, "ROUTING": {"TITLE": "Bepalen welke app", "SUB_TITLE_OBJECTIVE": "L’objectif de cette tâche est de définir si le dossier est traitable dans Wave ou s’il doit être traité dans le Mainframe.", "SUB_TITLE_INTEGRATION": "<PERSON><PERSON>t zullen de meest eenvoudige documenten worden geïntegreerd. De integratie van de complexere dossiers zal geleidelijk gebeuren.", "AUTOMATIC_FILTERS_TITLE": "Automatische verificaties", "AUTOMATIC_FILTERS_VALUE": "<PERSON><PERSON><PERSON><PERSON><PERSON> in Wave", "AUTOMATIC_FILTERS_DECISION": "<PERSON><PERSON><PERSON>", "MANUAL_FILTERS_TITLE": "Handmatige verificatie", "JUSTIFICATION": "Rechtvaardiging", "JUSTIFICATION_REQUIRED": "Er is rechtvaardiging nodig om door te kunnen gaan in het mainframe", "JUSTIFICATION_TITLE": "Twi<PERSON><PERSON><PERSON> over de Verificaties?", "JUSTIFICATION_SUB_TITLE": "Rechtvaardig het probleem en ga verder in het Mainframe", "MESSAGE_SENT_MF": "Ga nu naar S24 om het bestand te verwerken", "NEXT_TASK": {"DESCRIPTION": "Het dossier kan worden verwerkt in Wave. Ga nu verder met de volgende taak:", "ACTION": "“<PERSON><PERSON><PERSON><PERSON> van werknemers coderen”"}, "FILTERS": {"YES": "<PERSON>a", "NO": "<PERSON><PERSON>", "ANOTHER_INCOME_SOURCES": "De burger heeft andere inkomsten", "COMPLEMENTARY_ACTIVITY": "De burger heeft nevenactiviteiten", "HAS_MORE_THAN_ONE_OCCUPATION": "De burger heeft é<PERSON> beroep", "RELEVANT_TO_MEDICAL_REASONS": "Gaat over medische overmacht", "RELEVANT_TO_APPRENTICESHIP": "He<PERSON>t geen betrekking op een alternerende opleiding", "TRANSFER_BETWEEN_OP_OR_OC": "Heeft geen betrekking op aanvraag tot overgang van UI of WB", "RELEVANT_TO_PORT_WORKER": "He<PERSON>t geen betrekking op een havenarbeider", "CITIZEN_OVER_65_YEARS_OLD": "<PERSON><PERSON>t geen betrekking op een persoon ouder dan 65 jaar", "REQUEST_BEYOND_DEADLINES": "<PERSON><PERSON><PERSON><PERSON><PERSON> binnen de juiste termijn", "CASE_OF_IMPULSION": "<PERSON><PERSON>t geen betrekking op een Impulsion-dossier", "NON_BELGIAN_RESIDENT": "He<PERSON>t geen betrekking op een buitenlandse werknemer", "REQUEST_FOR_ECONOMIC_REASON": "Heeft geen betrekking op economische overmacht", "CITIZEN_WITH_SANCTION": "<PERSON><PERSON><PERSON> geen sancties met zich mee", "MISSING_DOCUMENTS": "Ontbrekende documenten:"}}}, "ERROR": {"NISS_CHECKSUM": "Ongeldig rijksregisternummer.", "INVALID_DATE": "Ongeldige datum.", "REQUIRED_FIELD": "Dit veld is verplicht.", "DATE_IN_FUTURE": "De datum mag niet in de toekomst liggen.", "FIELD_LENGTH": "Dit veld mag maximaal {{requiredLength}} tekens bevatten, maar het heeft er {{actualLength}}", "LENGTH_ERROR": "De lengte van dit veld is ongeldig.", "INVALID_FIELD": "De ingevoerde waarde is ongeldig.", "NO_IBAN_COUNTRY": "<PERSON>n <PERSON>-land"}, "C9_ANNEXES": {"TITLE": "Bijlagen:", "SCANNED_DOCUMENT": "Gescande documenten", "SCANNED_DOCUMENTS": "Gescande documenten", "EC1": "eC1", "EC32": "eC3.2 werknemer", "WECH": "WECH (ASR)"}, "TOASTS": {"SUCCESS": "Update uitgevoerd", "ERROR": "Er is een fout opgetreden", "C51_ERROR": "Er is een fout opgetreden bij het verzenden van de C51", "SEND_SUCCESS": "Succesvol verzonden"}, "TREATED_ON_MAINFRAME": {"CLOSED": "Het dossier is correct afgesloten.", "DECISION": "<PERSON> ", "BAREMA": "en het barema ", "VALIDATED": "<PERSON><PERSON>jn <PERSON> in de Mainframe", "CODE": {"C2Y": "C2Y - Gevalideerd en a<PERSON>vaard", "C2N": "C2N - Gevalideerd en geweigerd", "C2F": "C2F - Gevalideerd voor een datum in de toekomst", "C2P": "C2P - Aanv<PERSON>ag zonder impact", "C51": "C51 - Terugzending aan UI", "C9B": "C9Bis - Wach<PERSON> op de verwerking van een andere aanvraag", "C2": "C2 - <PERSON><PERSON><PERSON><PERSON>", "C9NA": "C9NA - C9 niet geaccepteerd"}}, "MAINFRAME_RESPONSE": {"OK": "De data zijn verzonden naar de Mainframe. U moet de S24 nog valideren.", "NOK": "De data zijn niet correct verzonden naar de Mainframe. Het ontwikkelteam is geïnformeerd. <PERSON><PERSON>eve contact op te nemen met support.", "PENDING": "Pending - tbd"}, "LOGICLY_DELETED": {"TITLE": "De gegevens voor dit proces zijn <strong>niet langer actueel</strong>. U kunt de nieuwe versie van de gegevens <a href='{{processLink}}' target='_blank'>hier</a> vinden ", "MESSAGE": "Ibri - <PERSON><PERSON><PERSON> van het sociale zekerheidssysteem"}}