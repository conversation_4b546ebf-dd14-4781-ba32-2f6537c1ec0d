{"name": "frontend", "version": "0.0.0", "scripts": {"ng": "ng", "start": "node scripts/prebuild-wo-mock-ui.js && ng serve --project cu-test-local --port 4300 --host 0.0.0.0", "start:nyc": "nyc npm start", "start:playwrightint": "node scripts/prebuild-wo-mock-ui.js && ng serve --project cu-test-local --configuration=playwright --port 4300 --host 0.0.0.0", "build": "ng build", "watch": "ng build --watch --configuration development", "build:web-components": "npm run build:components && npm run build:web-components:compile", "build:components": "ng build --project cu  --configuration=production", "build:web-components:compile": "ng run cu-app:build:production --output-hashing none", "startSync": "ng serve --project cu-test-local --port 8080 --host 0.0.0.0", "e2e": "ng e2e", "e2e:ci": "ng run cu-test-local:e2e-ci", "playwright:ci": "ng run cu-test-local:playwright-ci", "e2e:coverage": "npx nyc report --reporter=lcov --reporter=text-summary", "cypress:open": "cypress open --env backend_basepath=http://localhost:9091/api", "cypress:run": "cypress run --env backend_basepath=http://localhost:9091/api", "cypress:smokeTests": "cypress smokeTests", "cucumber:report": "node cucumber-html-report.js", "generate-api": "powershell -File scripts/generate_apis.ps1", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:coverage:lcov": "nyc report --reporter=lcov", "playwright-integration": "npx playwright test ./projects/cu-test-local/playwright/integration-tests/ && node convert-coverage.js"}, "private": true, "dependencies": {"@angular/animations": "^19.2.1", "@angular/cdk": "^19.2.2", "@angular/common": "^19.2.1", "@angular/compiler": "^19.2.1", "@angular/core": "^19.2.1", "@angular/elements": "^19.2.1", "@angular/forms": "^19.2.1", "@angular/material": "^19.2.2", "@angular/platform-browser": "^19.2.1", "@angular/platform-browser-dynamic": "^19.2.1", "@angular/router": "^19.2.1", "@ngrx/component-store": "^19.0.1", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@onemrvapublic/design-system": "^19.3.2", "@onemrvapublic/design-system-theme": "19.3.2", "cypress-keycloak-commands": "^1.2.0", "iban-ts": "0.10.0", "ibantools": "^4.5.1", "jest-monocart-coverage": "^1.1.1", "keycloak-angular": "^19.0.2", "keycloak-js": "^26.1.3", "lcov-result-merger": "^5.0.1", "ngx-mask": "^19.0.6", "playwright-test-coverage": "^1.2.12", "rxjs": "~7.8.2", "tslib": "^2.8.1", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^19.0.0", "@angular-builders/jest": "^19.0.0", "@angular-devkit/build-angular": "^19.2.1", "@angular/cli": "^19.2.1", "@angular/compiler-cli": "^19.2.1", "@badeball/cypress-cucumber-preprocessor": "^22.0.1", "@bahmutov/cypress-esbuild-preprocessor": "^2.2.4", "@cypress/browserify-preprocessor": "^3.0.2", "@cypress/code-coverage": "^3.13.12", "@cypress/schematic": "^3.0.0", "@istanbuljs/nyc-config-typescript": "^1.0.2", "@jest/globals": "^29.7.0", "@jsdevtools/coverage-istanbul-loader": "^3.0.5", "@onemrvapublic/cypress-axe-report": "^1.2.3", "@playwright/test": "^1.50.1", "@testing-library/angular": "^17.3.6", "@testing-library/dom": "^10.4.0", "@types/deep-equal": "^1.0.4", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.16", "@types/node": "^22.13.9", "cypress": "^13.13.1", "cypress-axe": "^1.6.0", "cypress-keycloak-commands": "^1.2.0", "cypress-slow-down": "^1.3.1", "cypress-wait-until": "^3.0.2", "fast-equals": "5.2.2", "istanbul-lib-coverage": "^3.2.2", "istanbul-lib-report": "^3.0.1", "istanbul-lib-source-maps": "^5.0.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-preset-angular": "^14.5.3", "multiple-cucumber-html-reporter": "^3.9.2", "ng-packagr": "^19.2.0", "nyc": "^17.1.0", "playwright": "^1.50.1", "playwright-bdd": "^8.2.0", "playwright-ng-schematics": "^2.0.1", "ts-jest": "^29.2.6", "typescript": "~5.8.2", "vite-plugin-istanbul": "^7.0.0"}, "cypress-cucumber-preprocessor": {"nonGlobalStepDefinitions": true, "stepDefinitions": "projects/cu-test-local/cypress/integration/**/*.{js,mjs,ts,tsx}", "json": {"enabled": true, "output": "projects/cu-test-local/cypress/cucumber-json/out.json"}}}