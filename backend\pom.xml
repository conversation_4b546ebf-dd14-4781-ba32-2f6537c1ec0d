<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>be.fgov.onerva.cu</groupId>
        <artifactId>cu-root</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>backend</artifactId>
    <name>cu backend</name>
    <description>Description</description>

    <properties>
        <apicurio-registry-maven-plugin.version>2.4.2.Final</apicurio-registry-maven-plugin.version>
        <jackson-databind-nullable.version>0.2.6</jackson-databind-nullable.version>
        <jacoco-maven-plugin.version>0.8.11</jacoco-maven-plugin.version>
        <jib-maven-plugin.version>3.4.2</jib-maven-plugin.version>
        <lombok.version>1.18.36</lombok.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <openapi-generator-maven-plugin.version>7.9.0</openapi-generator-maven-plugin.version>
        <plugin-toolchains.version>3.2.0</plugin-toolchains.version>
    </properties>
    <dependencies>
        <!-- Internal Modules -->
        <dependency>
            <groupId>be.fgov.onerva.cu</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- SPRING-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- RabbitMQ-->
        <dependency>
            <groupId>be.fgov.onerva.rabbitmq-consumer</groupId>
            <artifactId>rabbitmq-consumer-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>be.fgov.onerva.observability</groupId>
            <artifactId>observability-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-layout-template-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>${springdoc.version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>
        <dependency>
            <groupId>org.thymeleaf.extras</groupId>
            <artifactId>thymeleaf-extras-springsecurity6</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-validator</groupId>
            <artifactId>commons-validator</artifactId>
        </dependency>

        <!-- Flagsmith + Open Feature-->
        <dependency>
            <groupId>dev.openfeature</groupId>
            <artifactId>sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>dev.openfeature.contrib.providers</groupId>
            <artifactId>flagsmith</artifactId>
        </dependency>
        <dependency>
            <groupId>com.flagsmith</groupId>
            <artifactId>flagsmith-java-client</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!--  PROVIDED  -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-testcontainers</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>rabbitmq</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mssqlserver</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.orm</groupId>
            <artifactId>hibernate-envers</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-envers</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ninja-squad</groupId>
            <artifactId>springmockk</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Cucumber -->
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-java</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-junit-platform-engine</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.cucumber</groupId>
            <artifactId>cucumber-spring</artifactId>
            <scope>test</scope>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>io.cucumber</groupId>-->
        <!--            <artifactId>cucumber-junit-platform-engine</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>org.junit.platform</groupId>-->
        <!--            <artifactId>junit-platform-suite</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>io.cucumber</groupId>-->
        <!--            <artifactId>cucumber-spring</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>io.apicurio</groupId>
                    <artifactId>apicurio-registry-maven-plugin</artifactId>
                    <version>${apicurio-registry-maven-plugin.version}</version>
                    <configuration>
                        <registryUrl>https://api-registry.test.paas.onemrva.priv/apis/registry/v2</registryUrl>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>download</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>io.apicurio</groupId>
                <artifactId>apicurio-registry-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <groupId>be.fgov.onerva.person.backend</groupId>
                                    <artifactId>person-rest-api</artifactId>
                                    <file>${project.basedir}/target/api/person-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.person.backend</groupId>
                                    <artifactId>person-async-api</artifactId>
                                    <file>${project.basedir}/target/api/person-async-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.wothirdpartyapi</groupId>
                                    <artifactId>wo-thirdpartyt-api</artifactId>
                                    <file>${project.basedir}/target/api/wo-thirdparty-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.woconfigurator</groupId>
                                    <artifactId>wo-facade-api</artifactId>
                                    <file>${project.basedir}/target/api/wo-facade-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.woorganizationalchartapi</groupId>
                                    <artifactId>wo-organizational-chart-api</artifactId>
                                    <file>${project.basedir}/target/api/wo-organizational-chart-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.unemployment</groupId>
                                    <artifactId>c9-events</artifactId>
                                    <file>${project.basedir}/target/api/c9-events.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.unemployment</groupId>
                                    <artifactId>c9-public-api</artifactId>
                                    <file>${project.basedir}/target/api/c9-public-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.bareme</groupId>
                                    <artifactId>bareme-rest-api</artifactId>
                                    <file>${project.basedir}/target/api/barema-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.registerproxyservice</groupId>
                                    <artifactId>registerproxyservice-public-api</artifactId>
                                    <file>${project.basedir}/target/api/registry-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
                <configuration>
                    <container>
                        <jvmFlags>
                            <jvmFlag>-XX:InitialRAMPercentage=50.0</jvmFlag>
                            <jvmFlag>-XX:MaxRAMPercentage=50.0</jvmFlag>
                        </jvmFlags>
                        <ports>
                            <port>8080</port>
                        </ports>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                    <to>
                        <auth>
                            <password>${env.bamboo_nexus_secret}</password>
                            <username>${env.bamboo_nexus_user}</username>
                        </auth>
                        <image>docker-alpha.onemrva.priv/onemrva/cu-backend</image>
                    </to>
                    <from>
                        <image>docker-release.onemrva.priv/onemrva/eclipse-temurin:21-jdk-1.3.0</image>
                    </from>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator-maven-plugin.version}</version>
                <configuration>
                    <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                </configuration>
                <executions>
                    <execution>
                        <id>application</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>spring</generatorName>
                            <artifactId>cu-rest</artifactId>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                            <packageName>be.fgov.onerva.cu.rest.pub</packageName>
                            <apiPackage>be.fgov.onerva.cu.rest.pub.api</apiPackage>
                            <invokerPackage>be.fgov.onerva.cu.rest.pub.invoker</invokerPackage>
                            <modelPackage>be.fgov.onerva.cu.rest.pub.model</modelPackage>
                            <configOptions>
                                <useResponseEntity>false</useResponseEntity>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <singleContentTypes>false</singleContentTypes>
                                <openApiNullable>false</openApiNullable>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <additionalModelTypeAnnotations>
                                    @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
                                </additionalModelTypeAnnotations>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <inputSpec>${project.basedir}/../api/public/cu.yaml</inputSpec>
                        </configuration>
                    </execution>
                    <execution>
                        <id>config</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>spring</generatorName>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                            </typeMappings>
                            <modelPackage>backend.rest.model</modelPackage>
                            <configOptions>
                                <useResponseEntity>false</useResponseEntity>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <singleContentTypes>false</singleContentTypes>
                                <openApiNullable>false</openApiNullable>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <additionalModelTypeAnnotations>
                                    @com.fasterxml.jackson.annotation.JsonIgnoreProperties(ignoreUnknown = true)
                                </additionalModelTypeAnnotations>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <packageName>be.fgov.onerva.cu.rest.priv</packageName>
                            <apiPackage>be.fgov.onerva.cu.rest.priv.api</apiPackage>
                            <invokerPackage>be.fgov.onerva.cu.rest.priv.invoker</invokerPackage>
                            <modelPackage>be.fgov.onerva.cu.rest.priv.model</modelPackage>

                            <inputSpec>${project.basedir}/../api/private/cu-config.yaml</inputSpec>
                        </configuration>
                    </execution>
                    <execution>
                        <id>lookup-wppt-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/../api/external/lookup-wppt-api.yaml
                            </inputSpec>
                            <packageName>be.fgov.onerva.cu.backend.rest.client.lookup</packageName>
                            <apiPackage>be.fgov.onerva.cu.backend.rest.client.lookup.wppt.api</apiPackage>
                            <modelPackage>be.fgov.onerva.cu.backend.rest.client.lookup.wppt.rest.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.cu.backend.rest.client.lookup.wppt.invoker</invokerPackage>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>lookup.wppt-rest</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.cu.backend.rest.client.lookup.wppt.v1</basePackage>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>person-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/person-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.person</packageName>
                            <apiPackage>be.fgov.onerva.person.api</apiPackage>
                            <modelPackage>be.fgov.onerva.person.rest.model</modelPackage>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <invokerPackage>be.fgov.onerva.person.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>kotlin</generatorName>
                            <artifactId>person-rest</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <useSpringBoot3>true</useSpringBoot3>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <openApiNullable>false</openApiNullable>
                                <library>jvm-spring-restclient</library>
                                <useJakartaEe>true</useJakartaEe>
                                <serializationLibrary>jackson</serializationLibrary>
                                <useCoroutines>false</useCoroutines>
                                <enumPropertyNaming>UPPERCASE</enumPropertyNaming>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>barema-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/barema-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.barema</packageName>
                            <apiPackage>be.fgov.onerva.barema.api</apiPackage>
                            <modelPackage>be.fgov.onerva.barema.rest.model</modelPackage>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <invokerPackage>be.fgov.onerva.barema.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>barema-rest</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.person</basePackage>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>

                    <execution>
                        <id>wo-thirdparty-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/wo-thirdparty-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.wo-thirdparty</packageName>
                            <apiPackage>be.fgov.onerva.wo-thirdparty.api</apiPackage>
                            <modelPackage>be.fgov.onerva.wo-thirdparty.rest.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.wo-thirdparty.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>wo-thirdparty-rest</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.wo-thirdparty</basePackage>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                            </typeMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>wo-organizational-chart-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/wo-organizational-chart-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.wo.organizational.chart</packageName>
                            <apiPackage>be.fgov.onerva.wo.organizational.chart.api</apiPackage>
                            <modelPackage>be.fgov.onerva.wo.organizational.chart.rest.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.wo.organizational.chart.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>wo-organizational-chart-rest</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.wo.organizational.chart</basePackage>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                            </typeMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>wo-facade-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/wo-facade-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.wo.facade</packageName>
                            <apiPackage>be.fgov.onerva.wo.facade.api</apiPackage>
                            <modelPackage>be.fgov.onerva.wo.facade.rest.model</modelPackage>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <invokerPackage>be.fgov.onerva.wo.facade.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <enablePostProcessFile>true</enablePostProcessFile>
                            <generatorName>kotlin</generatorName>
                            <artifactId>wo-facade-api</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <useSpringBoot3>true</useSpringBoot3>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <openApiNullable>false</openApiNullable>
                                <library>jvm-spring-restclient</library>
                                <useJakartaEe>true</useJakartaEe>
                                <serializationLibrary>jackson</serializationLibrary>
                                <useCoroutines>false</useCoroutines>
                                <enumPropertyNaming>UPPERCASE</enumPropertyNaming>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=java.time.LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>c9-public-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/c9-public-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.unemployment.c9</packageName>
                            <apiPackage>be.fgov.onerva.unemployment.c9.api</apiPackage>
                            <modelPackage>be.fgov.onerva.unemployment.c9.rest.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.unemployment.c9.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>c9-public-api</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.unemployment.c9</basePackage>
                                <openApiNullable>false</openApiNullable> <!-- Disable JsonNullable -->
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>registerproxyservice-public-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/registry-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.registerproxyservice</packageName>
                            <apiPackage>be.fgov.onerva.registerproxyservice.api</apiPackage>
                            <modelPackage>be.fgov.onerva.registerproxyservice.rest.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.registerproxyservice.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>c9-public-api</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.registerproxyservice</basePackage>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-toolchains-plugin</artifactId>
                <version>${plugin-toolchains.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>toolchain</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <toolchains>
                        <jdk>
                            <version>${java.version}</version>
                        </jdk>
                    </toolchains>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.5</version>
                <configuration>
                    <!--                    <properties>-->
                    <!--                        <configurationParameters>-->
                    <!--                            cucumber.junit-platform.naming-strategy=long-->
                    <!--                            junit.jupiter.execution.parallel.enabled = true-->
                    <!--                            junit.jupiter.execution.parallel.mode.default = concurrent-->
                    <!--                        </configurationParameters>-->
                    <!--                    </properties>-->
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>3.2.5</version>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <properties>
                        <configurationParameters>
                            cucumber.junit-platform.naming-strategy=long
                        </configurationParameters>
                    </properties>
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <configuration>
                    <excludes>
                        <exclude>be/fgov/onerva/unemployment/c9/**</exclude>
                        <exclude>be/fgov/onerva/cu/backend/config/**</exclude>
                        <exclude>be/fgov/onerva/cu/backend/CuBackendApplication.*</exclude>
                        <exclude>be/fgov/onerva/cu/backend/security/OnemRvaUserContext.*</exclude>
                        <exclude>be/fgov/onerva/cu/backend/security/Roles.*</exclude>
                        <exclude>be/fgov/onerva/cu/backend/rest/**</exclude>
                        <exclude>be/fgov/onerva/cu/rest/**</exclude>
                        <exclude>be/fgov/onerva/wo/**</exclude>
                        <exclude>be/fgov/onerva/wo_thirdparty/**</exclude>
                        <exclude>be/fgov/onerva/person/**</exclude>
                        <exclude>be/fgov/onerva/barema/**</exclude>
                        <exclude>**/FrontendConfigurationController.*</exclude>
                        <exclude>be/fgov/onerva/cu/backend/**/domain/**</exclude>
                        <exclude>be/fgov/onerva/cu/backend/**/model/**</exclude>
                        <exclude>be/fgov/onerva/cu/backend/adapter/Exceptions.kt</exclude>
                        <exclude>be/fgov/onerva/registerproxyservice/**</exclude>
                    </excludes>
                    <rules>
                        <rule>
                            <element>CLASS</element>
                            <limits>
                                <limit>
                                    <counter>LINE</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.85</minimum>
                                </limit>
                                <limit>
                                    <counter>BRANCH</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.65</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>post-integration-test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-check</id>
                        <phase>post-integration-test</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <!--suppress UnresolvedMavenProperty -->
                            <skip>${maven.test.skip}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <configuration>
                    <args>
                        <arg>
                            -Xnullability-annotations=@jakarta.annotation.Nullable:strict
                        </arg>
                        <arg>-Xjsr305=strict</arg>
                        <arg>-Xjsr305=@jakarta.annotation.Nullable:strict</arg>
                        <!-- Add API version specification -->
                        <arg>-api-version=2.1</arg>
                        <arg>-language-version=2.1</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                        <plugin>jpa</plugin>
                    </compilerPlugins>
                    <jvmTarget>${java.version}</jvmTarget>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>process-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/main/java</source>
                                <source>src/main/kotlin</source>
                                <source>target/generated-sources/annotations</source>
                                <source>target/generated-sources/openapi/src/main/java</source>
                                <source>target/generated-sources/openapi/src/main/kotlin</source>
                            </sourceDirs>
                            <compilerPlugins>
                                <plugin>spring</plugin>
                                <plugin>jpa</plugin>
                            </compilerPlugins>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>process-test-sources</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <source>src/test/java</source>
                                <source>src/test/kotlin</source>
                                <source>target/generated-test-sources/test-annotations</source>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <encoding>${project.build.sourceEncoding}</encoding>
                            <source>${java.version}</source>
                            <target>${java.version}</target>
                            <release>${java.version}</release>
                            <annotationProcessorPaths>
                                <path>
                                    <groupId>org.projectlombok</groupId>
                                    <artifactId>lombok</artifactId>
                                    <version>${lombok.version}</version>
                                </path>
                            </annotationProcessorPaths>
                            <showWarnings>true</showWarnings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                        <configuration>
                            <encoding>${project.build.sourceEncoding}</encoding>
                            <source>${java.version}</source>
                            <target>${java.version}</target>
                            <release>${java.version}</release>
                            <annotationProcessorPaths>
                                <path>
                                    <groupId>org.projectlombok</groupId>
                                    <artifactId>lombok</artifactId>
                                    <version>${lombok.version}</version>
                                </path>
                            </annotationProcessorPaths>
                            <showWarnings>true</showWarnings>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>sync</id>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <scope>runtime</scope>
                    <optional>true</optional>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
