import {ComponentFixture, TestBed} from "@angular/core/testing";
import {HttpClient, HttpClientModule} from "@angular/common/http";
import {MatDialog} from "@angular/material/dialog";
import {TranslateModule, TranslateService, TranslateLoader, TranslateStore} from "@ngx-translate/core";
import {CuDialogComponent} from "../../components/cu-dialog/cu-dialog.component";
import {RedirectHandlerService} from "../../http/redirect-handler.service";
import {ToastService} from "../../services/toast.service";
import {DataValidationComponent} from "./data-validation.component";
import {ConfigService} from "../../config/config.service";
import {DataValidationService} from "../../http/data-validation.service";
import {GeoLookupService} from "../../http/geo-lookup.service";
import {DateAdapter} from "@angular/material/core";
import {of, throwError} from "rxjs";
import {ReactiveFormsModule} from "@angular/forms";
import {CommonModule} from "@angular/common";
import {Component, NO_ERRORS_SCHEMA} from "@angular/core";
import {FieldSource} from "@rest-client/cu-bff";

jest.mock("../../environments/environment", () => ({
    environment: {
        apiBasePath: "http://mock-api",
        production: false,
    },
}));

@Component({
    selector: "lib-cu-c9-annexes",
    template: "",
    standalone: true,
})
class MockCuC9AnnexesComponent {
}

@Component({
    selector: "lib-data-consistency",
    template: "",
    standalone: true,
})
class MockDataConsistencyComponent {
}

@Component({
    selector: "lib-loading-component",
    template: "",
    standalone: true,
})
class MockLoadingComponent {
}

@Component({
    selector: "lib-cu-closed-or-treated-on-main-frame",
    template: "",
    standalone: true,
})
class MockCuClosedOrTreatedOnMainFrameComponent {
}

describe("DataValidationComponent", () => {
    let component: DataValidationComponent;
    let fixture: ComponentFixture<DataValidationComponent>;
    let configServiceMock: Partial<ConfigService>;
    let dataValidationServiceMock: Partial<DataValidationService>;
    let geoLookupServiceMock: Partial<GeoLookupService>;
    let translateServiceMock: Partial<TranslateService>;
    let httpClientMock: Partial<HttpClient>;
    let toastServiceMock: Partial<ToastService>;
    let matDialogMock: Partial<MatDialog>;
    let redirectHandlerServiceMock: Partial<RedirectHandlerService>;
    let dialogRefMock: any;

    beforeEach(async () => {
        dialogRefMock = {
            close: jest.fn(),
            afterClosed: jest.fn().mockReturnValue(of(true)),
        };

        matDialogMock = {
            open: jest.fn().mockReturnValue(dialogRefMock),
        };

        redirectHandlerServiceMock = {
            initializeServices: jest.fn(),
            openS24Session: jest.fn().mockReturnValue(of({})),
            getC51RedirectUrl: jest.fn().mockReturnValue(of("http://test-url.com")),
        };

        configServiceMock = {
            isOnWO: jest.fn().mockReturnValue(false),
            isOnCi: jest.fn().mockReturnValue(false),
            getEnvironmentVariable: jest.fn().mockReturnValue("http://localhost:4200"),
            getWoDomain: jest.fn().mockReturnValue("http://wo-domain.com"),
        } as Partial<ConfigService>;

        dataValidationServiceMock = {
            initializeServices: jest.fn(),
            getAggregatedData: jest.fn().mockReturnValue(of({
                basicInfo: {
                    firstName: "John",
                    lastName: "Doe",
                },
                citizenInformation: {
                    dateOfBirth: "1980-01-01",
                    nationality: "BE",
                    fieldSources: [],
                },
                modeOfPayment: {
                    iban: "****************",
                    fieldSources: [],
                },
            })),
            selectCitizenInformationSources: jest.fn().mockReturnValue(of({})),
            selectModeOfPaymentSources: jest.fn().mockReturnValue(of({})),
            selectUnionContributionSources: jest.fn().mockReturnValue(of({})),
            closeTask: jest.fn().mockReturnValue(of({})),
        } as Partial<DataValidationService>;

        geoLookupServiceMock = {
            initializeService: jest.fn(),
        };

        translateServiceMock = {
            setDefaultLang: jest.fn(),
            use: jest.fn(),
            currentLang: "NL",
            instant: jest.fn().mockImplementation((key: string) => key),
        };

        toastServiceMock = {
            success: jest.fn(),
            error: jest.fn(),
        };

        httpClientMock = {
            get: jest.fn().mockReturnValue(of({})),
            post: jest.fn().mockReturnValue(of({})),
            put: jest.fn().mockReturnValue(of({})),
            delete: jest.fn().mockReturnValue(of({})),
            request: jest.fn().mockReturnValue(of({})),
        };

        await TestBed.configureTestingModule({
            imports: [
                HttpClientModule,
                CommonModule,
                ReactiveFormsModule,
                TranslateModule.forRoot({
                    loader: {
                        provide: TranslateLoader,
                        useValue: {
                            getTranslation: jest.fn().mockReturnValue(of({})),
                        },
                    },
                }),
                MockCuC9AnnexesComponent,
                MockDataConsistencyComponent,
                MockLoadingComponent,
                MockCuClosedOrTreatedOnMainFrameComponent,
            ],
            providers: [
                {provide: ConfigService, useValue: configServiceMock},
                {provide: DataValidationService, useValue: dataValidationServiceMock},
                {provide: GeoLookupService, useValue: geoLookupServiceMock},
                {provide: TranslateService, useValue: translateServiceMock},
                {provide: HttpClient, useValue: httpClientMock},
                {provide: ToastService, useValue: toastServiceMock},
                {provide: DateAdapter, useValue: {setLocale: jest.fn()}},
                {provide: MatDialog, useValue: matDialogMock},
                {provide: RedirectHandlerService, useValue: redirectHandlerServiceMock},
                TranslateStore,
            ],
            schemas: [NO_ERRORS_SCHEMA],
        })
            .compileComponents();

        fixture = TestBed.createComponent(DataValidationComponent);
        component = fixture.componentInstance;
        component.dataConsistencyData = {
            citizenInformation: {fieldSources: []},
            modeOfPayment: {fieldSources: []},
        } as any;
    });

    it("should create", () => {
        expect(component).toBeTruthy();
    });

    it("should emit appReady event on init", () => {
        const emitSpy = jest.spyOn((component as any).action, "emit");
        component.ngOnInit();
        expect(emitSpy).toHaveBeenCalledWith({messageType: "appReady"});
    });

    it("should clean up subscriptions on destroy", () => {
        const nextSpy = jest.spyOn(component.destroy$, "next");
        const completeSpy = jest.spyOn(component.destroy$, "complete");

        component.ngOnDestroy();

        expect(nextSpy).toHaveBeenCalled();
        expect(completeSpy).toHaveBeenCalled();
    });

    it("should set requestId correctly", () => {
        const testId = "test-123";
        const signalSpy = jest.spyOn(component["_requestIdSignal"], "set");

        component.requestId = testId;

        expect(component.requestId).toBe(testId);
        expect(signalSpy).toHaveBeenCalledWith(testId);
    });

    it("should set taskId correctly", () => {
        const testTaskId = "task-123";

        component.taskId = testTaskId;

        expect(component.taskId).toBe(testTaskId);
    });

    it("should set language correctly and use translate service", () => {
        const testLang = "fr";
        const translateSpy = jest.spyOn(component["translate"], "use");

        component.language = testLang;

        expect(component.language).toBe(testLang);
    });

    it("should set token correctly", () => {
        const testToken = "test-token";
        const signalSpy = jest.spyOn(component["_tokenSignal"], "set");

        component.token = testToken;

        expect(component.token).toBe(testToken);
        expect(signalSpy).toHaveBeenCalledWith(testToken);
    });

    it("should set status correctly", () => {
        const testStatus = "COMPLETED";

        component.status = testStatus;

        expect(component.status).toBe(testStatus);
    });

    it("should initialize component services", () => {
        const dataValidationSpy = jest.spyOn(component["dataValidationService"], "initializeServices");
        const redirectHandlerSpy = jest.spyOn(component["redirectHandlerService"], "initializeServices");
        component["initializeComponentServices"]("test-token");
        expect(dataValidationSpy).toHaveBeenCalledWith("test-token");
        expect(redirectHandlerSpy).toHaveBeenCalledWith("test-token");
    });

    it("should fetch data in getWoTaskById", async () => {
        const getAggregatedDataSpy = jest.spyOn(component["dataValidationService"], "getAggregatedData");
        const citizenDataSpy = jest.spyOn(component.citizenData, "set");

        await component["getWoTaskById"]("test-request-id");

        expect(getAggregatedDataSpy).toHaveBeenCalledWith("test-request-id");
        expect(citizenDataSpy).toHaveBeenCalled();
    });

    it("should handle field sources changes", () => {
        const testFieldSources: FieldSource[] = [
            {fieldName: "birthDate", source: "C1"},
            {fieldName: "address", source: "ONEM"},
        ];

        component.onFieldSourcesChange(testFieldSources);

        expect(component.selectedFieldSources).toEqual(testFieldSources);
        expect(component.hasChangesToSave).toBe(true);
    });

    it("should not save when no field sources are selected", () => {
        component.hasChangesToSave = false;
        component.selectedFieldSources = [];

        const citizenSpy = jest.spyOn(component["dataValidationService"], "selectCitizenInformationSources");
        const paymentSpy = jest.spyOn(component["dataValidationService"], "selectModeOfPaymentSources");

        component.save();

        expect(citizenSpy).not.toHaveBeenCalled();
        expect(paymentSpy).not.toHaveBeenCalled();
    });

    it("should save selected field sources", () => {
        component.hasChangesToSave = true;
        component.dataConsistencyData = {
            citizenInformation: {fieldSources: []},
            modeOfPayment: {fieldSources: []},
        } as any;
        component.requestId = "test-id";

        component.selectedFieldSources = [
            {fieldName: "birthDate", source: "C1"},
            {fieldName: "iban", source: "ONEM"},
        ];

        const citizenSpy = jest.spyOn(component["dataValidationService"], "selectCitizenInformationSources");
        const paymentSpy = jest.spyOn(component["dataValidationService"], "selectModeOfPaymentSources");
        const toastSpy = jest.spyOn(toastServiceMock, "success");

        component.save();

        expect(citizenSpy).toHaveBeenCalled();
        expect(paymentSpy).toHaveBeenCalled();
        expect(toastSpy).toHaveBeenCalledWith("TOASTS.SEND_SUCCESS");
    });

    it("should correctly map field sources during save", () => {
        component.hasChangesToSave = true;
        component.requestId = "test-id";
        component.dataConsistencyData = {
            citizenInformation: {fieldSources: []},
            modeOfPayment: {fieldSources: []},
        } as any;

        component.selectedFieldSources = [
            {fieldName: "birthDate", source: "C1"},
            {fieldName: "bankAccount", source: "ONEM"},
            {fieldName: "otherPersonName", source: "C1"},
        ];

        const citizenSpy = jest.spyOn(component["dataValidationService"], "selectCitizenInformationSources")
            .mockReturnValue(of({}));
        const paymentSpy = jest.spyOn(component["dataValidationService"], "selectModeOfPaymentSources")
            .mockReturnValue(of({}));

        component.save();

        expect(paymentSpy).toHaveBeenCalledWith(
            component.requestId,
            expect.arrayContaining([
                expect.objectContaining({fieldName: "account"}),
                expect.objectContaining({fieldName: "otherPersonName"}),
            ]),
        );
    });

    describe("openS24Session", () => {
        it("should open dialog with correct configuration", () => {
            component.requestId = "test-request-123";

            component.openS24Session();

            expect(matDialogMock.open).toHaveBeenCalledWith(CuDialogComponent, {
                data: expect.objectContaining({
                    title: expect.any(String),
                    content: expect.any(String),
                    primaryActionText: expect.any(String),
                    secondaryActionText: expect.any(String),
                    dialogType: "warn",
                    dialogSize: "medium",
                    onPrimaryAction: expect.any(Function),
                    onSecondaryAction: expect.any(Function),
                }),
            });
        });

        it("should call redirectHandlerService.openS24Session when primary action is clicked", () => {
            component.requestId = "test-request-123";

            component.openS24Session();

            const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
            const onPrimaryAction = dialogConfig.data.onPrimaryAction;

            onPrimaryAction();

            expect(redirectHandlerServiceMock.openS24Session).toHaveBeenCalledWith("test-request-123");
            expect(dialogRefMock.close).toHaveBeenCalledWith(true);
        });

        it("should close dialog with false when secondary action is clicked", () => {
            component.openS24Session();

            const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
            const onSecondaryAction = dialogConfig.data.onSecondaryAction;

            onSecondaryAction();

            expect(dialogRefMock.close).toHaveBeenCalledWith(false);
        });
    });

    describe("validateAndContinue", () => {
        it("should call save when there are changes to save", () => {
            component.hasChangesToSave = true;
            component.selectedFieldSources = [{fieldName: "test", source: "C1"}];
            const saveSpy = jest.spyOn(component, "save");

            component.validateAndContinue();

            expect(saveSpy).toHaveBeenCalled();
        });

        it("should call save but save should return early when there are no changes", () => {
            component.hasChangesToSave = false;
            component.selectedFieldSources = [];

            const saveSpy = jest.spyOn(component, 'save');
            const selectCitizenSpy = jest.spyOn(dataValidationServiceMock, 'selectCitizenInformationSources');
            const selectPaymentSpy = jest.spyOn(dataValidationServiceMock, 'selectModeOfPaymentSources');

            component.validateAndContinue();

            expect(saveSpy).toHaveBeenCalled();

            expect(selectCitizenSpy).not.toHaveBeenCalled();
            expect(selectPaymentSpy).not.toHaveBeenCalled();
        });

        it("should open dialog with correct configuration", () => {
            component.validateAndContinue();

            expect(matDialogMock.open).toHaveBeenCalledWith(CuDialogComponent, {
                data: expect.objectContaining({
                    title: expect.any(String),
                    content: expect.stringContaining("validate"),
                    primaryActionText: expect.any(String),
                    secondaryActionText: expect.any(String),
                    dialogType: "warn",
                    dialogSize: "medium",
                    onPrimaryAction: expect.any(Function),
                    onSecondaryAction: expect.any(Function),
                }),
            });
        });

        it("should close task when primary action is clicked", (done) => {
            component.requestId = "test-request-123";

            component.validateAndContinue();

            const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
            const onPrimaryAction = dialogConfig.data.onPrimaryAction;

            onPrimaryAction();

            setTimeout(() => {
                expect(dataValidationServiceMock.closeTask).toHaveBeenCalledWith(
                    "test-request-123",
                    "VALIDATION_DATA",
                );
                expect(toastServiceMock.success).toHaveBeenCalledWith("TOASTS.SEND_SUCCESS");
                expect(dialogRefMock.close).toHaveBeenCalledWith(true);
                done();
            }, 0);
        });

        it("should handle error when closing task fails", (done) => {
            const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
            dataValidationServiceMock.closeTask = jest.fn().mockReturnValue(
                throwError(() => new Error("Close task failed")),
            );
            component.requestId = "test-request-123";

            component.validateAndContinue();

            const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
            const onPrimaryAction = dialogConfig.data.onPrimaryAction;

            onPrimaryAction();

            setTimeout(() => {
                expect(consoleErrorSpy).toHaveBeenCalledWith("Error closing task:", expect.any(Error));
                expect(dialogRefMock.close).toHaveBeenCalledWith(false);
                done();
            }, 0);
        });

        it("should close dialog with false when secondary action is clicked", () => {
            component.validateAndContinue();

            const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
            const onSecondaryAction = dialogConfig.data.onSecondaryAction;

            onSecondaryAction();

            expect(dialogRefMock.close).toHaveBeenCalledWith(false);
        });
    });

    describe("handleRegisVerificationChange", () => {
        it("should enable action buttons when verified", () => {
            component.handleRegisVerificationChange(true);
            expect(component.areActionButtonsEnabled).toBe(true);
        });

        it("should disable action buttons when not verified", () => {
            component.handleRegisVerificationChange(false);
            expect(component.areActionButtonsEnabled).toBe(false);
        });
    });

    describe("onTableConsistencyChanged", () => {
        it("should update isTableConsistent when table consistency changes", () => {
            component.onTableConsistencyChanged(true);
            expect(component.isTableConsistent).toBe(true);
        });

        it("should update isTableConsistent to false when table is inconsistent", () => {
            component.onTableConsistencyChanged(false);
            expect(component.isTableConsistent).toBe(false);
        });
    });

    describe("sendC51", () => {
        beforeEach(() => {
            redirectHandlerServiceMock.getC51RedirectUrl = jest.fn().mockReturnValue(of("http://test-url.com"));
        });
        it("should open new window with C51 redirect URL", () => {
            const windowOpenSpy = jest.spyOn(window, "open").mockImplementation();
            component.requestId = "test-request-123";

            component.sendC51();

            expect(redirectHandlerServiceMock.getC51RedirectUrl).toHaveBeenCalledWith("test-request-123");
            expect(windowOpenSpy).toHaveBeenCalledWith("http://test-url.com", "_blank");
        });

        it("should handle error when getting C51 redirect URL fails", () => {
            const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
            redirectHandlerServiceMock.getC51RedirectUrl = jest.fn().mockReturnValue(
                throwError(() => new Error("C51 redirect failed")),
            );
            component.requestId = "test-request-123";

            component.sendC51();

            expect(toastServiceMock.error).toHaveBeenCalledWith("TOASTS.C51_ERROR");
            expect(consoleErrorSpy).toHaveBeenCalledWith(
                "Error getting C51 redirect URL:",
                expect.any(Error),
            );
        });
    });
});