package be.fgov.onerva.cu.backend.application.domain

import java.time.LocalDate

/**
 * Command object representing a received change of address request.
 * This command captures all information needed to initiate a change of address process.
 * This command gathers all inforamtion received from the C9 messaage.
 *
 * @property c9Id Unique identifier for the C9 form
 * @property type Type of the C9
 * @property ssin Social Security Identification Number of the requester
 * @property ec1Id Identifier of the identity document
 */
data class ChangePersonalDataRequestReceivedCommand(
    val c9Id: Long,
    val type: String,
    val ssin: String,
    val ec1Id: Int?,
)

data class ChangePersonalDataRequestTreatedCommand(
    val c9Id: Long,
    val type: String,
    val ssin: String,
    val decisionType: DecisionType,
    val decisionDate: LocalDate,
    val user: String,
)

/**
 * Data structure for persisting a change of address request.
 * This model represents the minimal set of data required for storage in the system.
 *
 * @property c9id Associated C9 form identifier
 * @property type Type of the C9
 * @property ssin Social Security Identification Number of the citizen
 * @property firstName First name of the citizen
 * @property lastName Last name of the citizen
 * @property sectOp Section operator code
 * @property opKey Operation key identifier
 * @property numbox Citizen's numbox identifier
 * @property receptionDate Date when the request was received by the system
 * @property requestDate Date when the change was requested
 * @property documentType The document type (Electronic or Paper)
 * @property paymentInstitution Identifier of the payment institution
 * @property entityCode Optional code identifying the processing entity
 * @property dossierId Identifier of the associated dossier
 * @property scanUrl Url of the scanned documents
 */
data class ChangePersonalDataPersistCommand(
    val c9id: Long,
    val type: String,
    val ssin: String,
    val sectOp: String,
    val opKey: String,
    val numbox: Int,
    val receptionDate: LocalDate,
    val requestDate: LocalDate,
    val paymentInstitution: Int,
    val entityCode: String?,
    val dossierId: String,
    val documentType: IdentityDocumentType,
    val citizenInformation: CitizenInformation?,
    val modeOfPayment: ModeOfPayment?,
    val unionContribution: UnionContribution?,
    val scanUrl: String?,
)

data class CreateChangePersonalDataTaskCommand(
    val c9id: Long,
    val type: String,
    val ssin: String,
    val numbox: Int,
    val receptionDate: LocalDate,
    val entityCode: String?,
    val requestDate: LocalDate,
    val dossierId: String,
    val paymentInstitution: Int,
    val sectOp: String,
)

data class UpdateChangePersonalDataDecisionCommand(
    val decisionType: DecisionType,
    val decisionDate: LocalDate,
    val user: String,
    val decisionBarema: String?,
)
