## Relevant Files

- `helm/environments/values-prod.yaml` - Production-specific Helm values configuration file
- `helm/cu/values.yaml` - Base Helm chart values (for reference)
- `skaffold.yaml` - Skaffold configuration that needs prod profile
- `azure-pipelines.yml` - CI/CD pipeline configuration for prod deployment
- `helm/cu/Chart.yaml` - Main Helm chart definition
- `backend/pom.xml` - Backend Maven configuration (for image references)
- `bff/pom.xml` - BFF Maven configuration (for image references)
- `helm/cu/templates/*.yaml` - Helm templates that use the values

### Notes
- Production values should follow the security best practices established in test and val environments
- Sensitive values (passwords, secrets) should use placeholders that get injected via CI/CD
- Image registries should point to production-grade registries (docker-release vs docker-alpha/beta)
- Ensure all external service URLs point to production endpoints

## Tasks

- [ ] 1.0 Create Production Helm Values Configuration
  - [ ] 1.1 Copy values-val.yaml as template for values-prod.yaml
  - [ ] 1.2 Update global.routes.host to production URL (cu.prod.paas.onemrva.priv)
  - [ ] 1.3 Set image registries to docker-release.onemrva.priv for all components
  - [ ] 1.4 Remove debug logging configurations (LOGGING_LEVEL_BE_FGOV_ONERVA_CU)
  - [ ] 1.5 Ensure spring.profiles.active is set to "prod" for backend and bff

- [ ] 2.0 Configure Production-Specific Service Endpoints
  - [ ] 2.1 Update Keycloak URLs to production endpoints (https://keycloak.prod.paas.onemrva.priv)
  - [ ] 2.2 Configure WO service URLs (wo-thirdparty-api, wo-organizational-chart-api, wo-configurator)
  - [ ] 2.3 Set production endpoints for person, bareme, c9, registry services
  - [ ] 2.4 Update RabbitMQ host to production cluster
  - [ ] 2.5 Configure Flagsmith API key and URL for production environment
  - [ ] 2.6 Set production URLs for c51 and regis applications

- [ ] 3.0 Set Up Production Security and Secrets Management
  - [ ] 3.1 Configure OAuth2 client secrets with placeholder values for CI/CD injection
  - [ ] 3.2 Set up database password placeholders for secure injection
  - [ ] 3.3 Configure JWT issuer URIs for production Keycloak realm
  - [ ] 3.4 Update redirect URIs to use production domain
  - [ ] 3.5 Enable production security features (keycloak.checktoken: true)
  - [ ] 3.6 Configure service account credentials for backend and bff

- [ ] 4.0 Configure Production Database and Infrastructure Settings
  - [ ] 4.1 Set production database connection string with proper failover configuration
  - [ ] 4.2 Configure production database username and schema
  - [ ] 4.3 Set appropriate Liquibase contexts (ddl,dml,ddl-prod,dml-prod)
  - [ ] 4.4 Disable RabbitMQ OAuth if not required in production
  - [ ] 4.5 Configure production-grade resource limits and requests
  - [ ] 4.6 Set production-appropriate health check timeouts and thresholds

- [ ] 5.0 Update CI/CD Pipeline for Production Deployment
  - [ ] 5.1 Add production stage to azure-pipelines.yml after val stage
  - [ ] 5.2 Configure production variable group with secure variable references
  - [ ] 5.3 Add production deployment job using buildsh/deploy template
  - [ ] 5.4 Set up production-specific environment variables for secrets injection
  - [ ] 5.5 Add appropriate deployment conditions (succeeded on val, isDevelop true)
  - [ ] 5.6 Configure production approval gates if required
